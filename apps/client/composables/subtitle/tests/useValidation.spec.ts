import { describe, test, expect, vi, beforeEach } from 'vitest'
import type { Subtitle } from '~/types/subtitle/subtitle'

// Mock the parseSrtTime function and other dependencies
vi.mock('../useTimeUtils', () => ({
  parseSrtTime: vi.fn(),
  validateSrtTimeFormat: vi.fn(() => true),
}))

import { 
  validateSubtitleTiming, 
  validateSubtitleOrder,
  isSrtTimeFormat,
  isVttTimeFormat,
  isLrcTimeFormat,
  isValidMimeType,
  isAudioFile,
  isVideoFile,
  isSubtitleFile,
  isMediaFile,
  getFileExtension,
  isValidHexColor,
  isNumericString,
  isPositiveIntegerString,
  isValidFileSize,
  isValidUrl,
  isValidEmail,
  isValidLength,
  hasOnlyAllowedChars,
  isValidTimestamp,
  isValidTimeString,
  validateSubtitleContent,
  validateSubtitleContentDetailed,
  validateComplete,
  validateSubtitleTimeRange,
  detectTranslationErrors,
  hasFormatIssues,
  checkTranslationQuality,
  calculateQualityScore,
  calculateConfidence,
  generateSuggestions,
  checkSubtitleQuality,
  batchCheckSubtitleQuality,
} from '../useValidation'
import { parseSrtTime } from '../useTimeUtils'

const mockParseSrtTime = parseSrtTime as unknown as ReturnType<typeof vi.fn>

describe('useValidation - 时间戳验证优化测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // 设置默认的时间解析行为
    mockParseSrtTime.mockImplementation((time: string) => {
      if (time === '00:00:01,000') return 1000
      if (time === '00:00:02,000') return 2000
      if (time === '00:00:03,000') return 3000
      if (time === '00:00:03,500') return 3500
      if (time === '00:00:04,000') return 4000
      if (time === '00:00:05,000') return 5000
      if (time === 'invalid-time') return null
      return 0
    })
  })

  describe('validateSubtitleTiming - 时间重叠支持测试', () => {
    test('应该允许开始时间等于上一行结束时间（歌词场景）', () => {
      const subtitles: Subtitle[] = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:03,000',
          text: 'First line',
          translationText: '',
        },
        {
          uuid: '2',
          id: 2,
          startTime: '00:00:03,000', // 等于上一行结束时间
          endTime: '00:00:05,000',
          text: 'Second line',
          translationText: '',
        },
      ]

      const result = validateSubtitleTiming(subtitles)
      expect(result.isValid).toBe(true)
      expect(result.timeErrors).toHaveLength(0)
    })

    test('应该允许轻微的时间重叠（歌词场景）', () => {
      const subtitles: Subtitle[] = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:03,500',
          text: 'First line',
          translationText: '',
        },
        {
          uuid: '2',
          id: 2,
          startTime: '00:00:03,000', // 早于上一行结束时间0.5秒
          endTime: '00:00:05,000',
          text: 'Second line',
          translationText: '',
        },
      ]

      const result = validateSubtitleTiming(subtitles)
      expect(result.isValid).toBe(true)
      expect(result.timeErrors).toHaveLength(0)
    })

    test('应该允许结束时间等于下一行开始时间（歌词场景）', () => {
      const subtitles: Subtitle[] = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:03,000',
          text: 'First line',
          translationText: '',
        },
        {
          uuid: '2',
          id: 2,
          startTime: '00:00:03,000', // 等于上一行结束时间
          endTime: '00:00:05,000',
          text: 'Second line',
          translationText: '',
        },
      ]

      const result = validateSubtitleTiming(subtitles)
      expect(result.isValid).toBe(true)
      expect(result.timeErrors).toHaveLength(0)
    })

    test('仍应检测时间格式错误', () => {
      mockParseSrtTime.mockImplementation((time: string) => {
        if (time === 'invalid-time') throw new Error('Invalid time format')
        return 1000
      })

      const subtitles: Subtitle[] = [
        {
          uuid: '1',
          id: 1,
          startTime: 'invalid-time',
          endTime: '00:00:02,000',
          text: 'First line',
          translationText: '',
        },
      ]

      const result = validateSubtitleTiming(subtitles)
      expect(result.isValid).toBe(false)
      expect(result.timeErrors).toHaveLength(1)
      expect(result.timeErrors[0].errorType).toBe('时间格式错误')
    })

    test('仍应检测同行内开始时间大于等于结束时间的错误', () => {
      mockParseSrtTime.mockImplementation((time: string) => {
        if (time === '00:00:03,000') return 3000
        if (time === '00:00:02,000') return 2000
        return 0
      })

      const subtitles: Subtitle[] = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:03,000', // 开始时间晚于结束时间
          endTime: '00:00:02,000',
          text: 'First line',
          translationText: '',
        },
      ]

      const result = validateSubtitleTiming(subtitles)
      expect(result.isValid).toBe(false)
      expect(result.timeErrors).toHaveLength(1)
      expect(result.timeErrors[0].errorType).toBe('开始时间不能大于或等于结束时间')
    })

    test('仍应检测超过音频总时长的错误', () => {
      const maxDuration = 4000 // 4秒
      const subtitles: Subtitle[] = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:05,000', // 超过最大时长
          text: 'First line',
          translationText: '',
        },
      ]

      const result = validateSubtitleTiming(subtitles, maxDuration)
      expect(result.isValid).toBe(false)
      expect(result.timeErrors).toHaveLength(1)
      expect(result.timeErrors[0].errorType).toBe('不能超过音频总时长')
    })

    test('复杂的歌词场景：多行时间重叠', () => {
      const subtitles: Subtitle[] = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:03,500',
          text: 'Line 1',
          translationText: '',
        },
        {
          uuid: '2',
          id: 2,
          startTime: '00:00:03,000', // 重叠0.5秒
          endTime: '00:00:05,000',
          text: 'Line 2',
          translationText: '',
        },
        {
          uuid: '3',
          id: 3,
          startTime: '00:00:04,000', // 重叠1秒
          endTime: '00:00:05,000',
          text: 'Line 3',
          translationText: '',
        },
      ]

      const result = validateSubtitleTiming(subtitles)
      expect(result.isValid).toBe(true)
      expect(result.timeErrors).toHaveLength(0)
    })
  })

  describe('validateSubtitleOrder - 顺序验证测试', () => {
    test('严格顺序验证仍应检测时间顺序错误', () => {
      const subtitles: Subtitle[] = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:03,500',
          text: 'First line',
          translationText: '',
        },
        {
          uuid: '2',
          id: 2,
          startTime: '00:00:03,000',
          endTime: '00:00:05,000',
          text: 'Second line',
          translationText: '',
        },
      ]

      const result = validateSubtitleOrder(subtitles)
      expect(result.valid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].message).toContain('开始时间早于')
    })

    test('正确的时间顺序应该通过验证', () => {
      const subtitles: Subtitle[] = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:03,000',
          text: 'First line',
          translationText: '',
        },
        {
          uuid: '2',
          id: 2,
          startTime: '00:00:03,000',
          endTime: '00:00:05,000',
          text: 'Second line',
          translationText: '',
        },
      ]

      const result = validateSubtitleOrder(subtitles)
      expect(result.valid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })
  })

  describe('边界情况测试', () => {
    test('空字幕数组应该通过验证', () => {
      const result = validateSubtitleTiming([])
      expect(result.isValid).toBe(true)
      expect(result.timeErrors).toHaveLength(0)
    })

    test('单个字幕应该通过验证', () => {
      const subtitles: Subtitle[] = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:03,000',
          text: 'Single line',
          translationText: '',
        },
      ]

      const result = validateSubtitleTiming(subtitles)
      expect(result.isValid).toBe(true)
      expect(result.timeErrors).toHaveLength(0)
    })

    test('null时间解析结果应该跳过验证', () => {
      mockParseSrtTime.mockReturnValue(null)
      
      const subtitles: Subtitle[] = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:03,000',
          text: 'Line with null time',
          translationText: '',
        },
      ]

      const result = validateSubtitleTiming(subtitles)
      expect(result.isValid).toBe(true)
      expect(result.timeErrors).toHaveLength(0)
    })
  })

  // ============================================================================
  // 格式验证函数测试
  // ============================================================================

  describe('格式验证函数', () => {
    describe('isSrtTimeFormat', () => {
      test('应该验证正确的 SRT 时间格式', () => {
        expect(isSrtTimeFormat('00:00:00,000')).toBe(true)
        expect(isSrtTimeFormat('01:23:45,678')).toBe(true)
        expect(isSrtTimeFormat('23:59:59,999')).toBe(true)
      })

      test('应该拒绝无效的 SRT 时间格式', () => {
        expect(isSrtTimeFormat('1:2:3,4')).toBe(false)
        expect(isSrtTimeFormat('01:23:45.678')).toBe(false) // 点分隔符
        expect(isSrtTimeFormat('25:00:00,000')).toBe(false) // 超过24小时
        expect(isSrtTimeFormat('00:60:00,000')).toBe(false) // 超过60分钟
        expect(isSrtTimeFormat('00:00:60,000')).toBe(false) // 超过60秒
        expect(isSrtTimeFormat('00:00:00,1000')).toBe(false) // 超过999毫秒
      })
    })

    describe('isVttTimeFormat', () => {
      test('应该验证正确的 VTT 时间格式', () => {
        expect(isVttTimeFormat('00:00:00.000')).toBe(true)
        expect(isVttTimeFormat('1:23:45.678')).toBe(true)
        expect(isVttTimeFormat('12:59:59.999')).toBe(true) // VTT 允许小时超过23，但格式必须是 1-2 位数字
      })

      test('应该拒绝无效的 VTT 时间格式', () => {
        expect(isVttTimeFormat('1:2:3.4')).toBe(false)
        expect(isVttTimeFormat('01:23:45,678')).toBe(false) // 逗号分隔符
        expect(isVttTimeFormat('00:60:00.000')).toBe(false) // 超过60分钟
        expect(isVttTimeFormat('00:00:60.000')).toBe(false) // 超过60秒
        expect(isVttTimeFormat('00:00:00.1000')).toBe(false) // 超过999毫秒
      })
    })

    describe('isLrcTimeFormat', () => {
      test('应该验证正确的 LRC 时间格式', () => {
        expect(isLrcTimeFormat('[00:00.00]')).toBe(true)
        expect(isLrcTimeFormat('[01:23.45]')).toBe(true)
        expect(isLrcTimeFormat('[59:59.99]')).toBe(true)
      })

      test('应该拒绝无效的 LRC 时间格式', () => {
        expect(isLrcTimeFormat('[1:2.3]')).toBe(false)
        expect(isLrcTimeFormat('01:23.45')).toBe(false) // 缺少方括号
        expect(isLrcTimeFormat('[60:00.00]')).toBe(false) // 超过60分钟
        expect(isLrcTimeFormat('[00:60.00]')).toBe(false) // 超过60秒
        expect(isLrcTimeFormat('[00:00.100]')).toBe(false) // 超过99厘秒
      })
    })

    describe('isValidMimeType', () => {
      test('应该验证 MIME 类型', () => {
        const file = new File([''], 'test.mp3', { type: 'audio/mp3' })
        expect(isValidMimeType(file, ['audio/mp3', 'audio/wav'])).toBe(true)
        expect(isValidMimeType(file, ['video/mp4'])).toBe(false)
      })
    })

    describe('文件格式验证', () => {
      test('isAudioFile 应该识别音频文件', () => {
        expect(isAudioFile('test.mp3')).toBe(true)
        expect(isAudioFile('test.wav')).toBe(true)
        expect(isAudioFile('test.m4a')).toBe(true)
        expect(isAudioFile('test.ogg')).toBe(true)
        expect(isAudioFile('test.flac')).toBe(true)
        expect(isAudioFile('test.aac')).toBe(true)
        expect(isAudioFile('test.txt')).toBe(false)
      })

      test('isVideoFile 应该识别视频文件', () => {
        expect(isVideoFile('test.mp4')).toBe(true)
        expect(isVideoFile('test.mov')).toBe(true)
        expect(isVideoFile('test.avi')).toBe(true)
        expect(isVideoFile('test.mkv')).toBe(true)
        expect(isVideoFile('test.webm')).toBe(true)
        expect(isVideoFile('test.wmv')).toBe(true)
        expect(isVideoFile('test.txt')).toBe(false)
      })

      test('isSubtitleFile 应该识别字幕文件', () => {
        expect(isSubtitleFile('test.srt')).toBe(true)
        expect(isSubtitleFile('test.vtt')).toBe(true)
        expect(isSubtitleFile('test.lrc')).toBe(true)
        expect(isSubtitleFile('test.ass')).toBe(true)
        expect(isSubtitleFile('test.ssa')).toBe(true)
        expect(isSubtitleFile('test.txt')).toBe(false)
      })

      test('isMediaFile 应该识别媒体文件', () => {
        expect(isMediaFile('test.mp3')).toBe(true)
        expect(isMediaFile('test.mp4')).toBe(true)
        expect(isMediaFile('test.txt')).toBe(false)
      })

      test('getFileExtension 应该获取文件扩展名', () => {
        expect(getFileExtension('test.mp3')).toBe('.mp3')
        expect(getFileExtension('test.MP3')).toBe('.mp3')
        expect(getFileExtension('path/to/file.txt')).toBe('.txt')
        expect(getFileExtension('file')).toBe('file') // 没有扩展名时返回原文件名
      })
    })

    describe('其他格式验证', () => {
      test('isValidHexColor 应该验证十六进制颜色', () => {
        expect(isValidHexColor('#FF0000')).toBe(true)
        expect(isValidHexColor('#f00')).toBe(true)
        expect(isValidHexColor('#123ABC')).toBe(true)
        expect(isValidHexColor('FF0000')).toBe(false) // 缺少 #
        expect(isValidHexColor('#GG0000')).toBe(false) // 无效字符
        expect(isValidHexColor('#FF00')).toBe(false) // 长度不对
      })

      test('isNumericString 应该验证数字字符串', () => {
        expect(isNumericString('123')).toBe(true)
        expect(isNumericString('123.45')).toBe(true)
        expect(isNumericString('-123')).toBe(true)
        expect(isNumericString('0')).toBe(true)
        expect(isNumericString('')).toBe(false)
        expect(isNumericString('abc')).toBe(false)
        expect(isNumericString('Infinity')).toBe(false)
        expect(isNumericString('NaN')).toBe(false)
        expect(isNumericString(' 123 ')).toBe(false) // 有空格
      })

      test('isPositiveIntegerString 应该验证正整数字符串', () => {
        expect(isPositiveIntegerString('123')).toBe(true)
        expect(isPositiveIntegerString('1')).toBe(true)
        expect(isPositiveIntegerString('0')).toBe(false) // 不是正数
        expect(isPositiveIntegerString('-1')).toBe(false) // 负数
        expect(isPositiveIntegerString('123.45')).toBe(false) // 有小数点
        expect(isPositiveIntegerString('+123')).toBe(false) // 有正号
        expect(isPositiveIntegerString('1e2')).toBe(false) // 科学记数法
        expect(isPositiveIntegerString(' 123 ')).toBe(false) // 有空格
      })

      test('isValidFileSize 应该验证文件大小', () => {
        const smallFile = new File(['small'], 'small.txt')
        const largeFile = new File([new ArrayBuffer(2 * 1024 * 1024)], 'large.txt')
        
        expect(isValidFileSize(smallFile, 1)).toBe(true)
        expect(isValidFileSize(largeFile, 1)).toBe(false)
      })

      test('isValidUrl 应该验证 URL', () => {
        // 在测试环境中可能需要polyfill URL构造函数
        if (typeof globalThis.URL === 'undefined') {
          globalThis.URL = class URL {
            constructor(url: string) {
              if (!url || !url.includes('://')) {
                throw new Error('Invalid URL')
              }
            }
          }
        }
        
        expect(isValidUrl('https://example.com')).toBe(true)
        expect(isValidUrl('http://example.com')).toBe(true)
        expect(isValidUrl('ftp://example.com')).toBe(true)
        expect(isValidUrl('not-a-url')).toBe(false)
        expect(isValidUrl('')).toBe(false)
      })

      test('isValidEmail 应该验证邮箱', () => {
        expect(isValidEmail('<EMAIL>')).toBe(true)
        expect(isValidEmail('<EMAIL>')).toBe(true)
        expect(isValidEmail('invalid-email')).toBe(false)
        expect(isValidEmail('test@')).toBe(false)
        expect(isValidEmail('@example.com')).toBe(false)
        expect(isValidEmail('test@.com')).toBe(false)
      })

      test('isValidLength 应该验证字符串长度', () => {
        expect(isValidLength('hello', 1, 10)).toBe(true)
        expect(isValidLength('hello', 5, 5)).toBe(true)
        expect(isValidLength('hello', 6, 10)).toBe(false)
        expect(isValidLength('hello', 1, 4)).toBe(false)
      })

      test('hasOnlyAllowedChars 应该验证允许的字符', () => {
        expect(hasOnlyAllowedChars('hello123', /^[a-z0-9]+$/)).toBe(true)
        expect(hasOnlyAllowedChars('hello123!', /^[a-z0-9]+$/)).toBe(false)
      })

      test('isValidTimestamp 应该验证时间戳', () => {
        expect(isValidTimestamp(1000)).toBe(true)
        expect(isValidTimestamp(0)).toBe(true)
        expect(isValidTimestamp(-1)).toBe(false)
        expect(isValidTimestamp(1000, 2000)).toBe(true)
        expect(isValidTimestamp(3000, 2000)).toBe(false)
      })

      test('isValidTimeString 应该验证时间字符串', () => {
        expect(isValidTimeString('00:00:00,000')).toBe(true) // SRT
        expect(isValidTimeString('00:00:00.000')).toBe(true) // VTT
        expect(isValidTimeString('[00:00.00]')).toBe(true) // LRC
        expect(isValidTimeString('1:23')).toBe(true) // MM:SS
        expect(isValidTimeString('1:23:45')).toBe(true) // HH:MM:SS
        expect(isValidTimeString('123.456')).toBe(true) // 秒数
        expect(isValidTimeString('invalid')).toBe(false)
      })
    })
  })

  // ============================================================================
  // 字幕验证函数测试
  // ============================================================================

  describe('字幕验证函数', () => {
    describe('validateSubtitleContent', () => {
      test('应该验证字幕内容', () => {
        const validSubtitles: Subtitle[] = [
          {
            uuid: '1',
            id: 1,
            startTime: '00:00:01,000',
            endTime: '00:00:02,000',
            text: 'Hello',
            translationText: '你好',
          },
        ]
        
        const invalidSubtitles: Subtitle[] = [
          {
            uuid: '1',
            id: 1,
            startTime: '00:00:01,000',
            endTime: '00:00:02,000',
            text: '',
            translationText: '你好',
          },
        ]

        expect(validateSubtitleContent(validSubtitles)).toBe(true)
        expect(validateSubtitleContent(invalidSubtitles)).toBe(false)
      })
    })

    describe('validateSubtitleContentDetailed', () => {
      test('应该详细验证字幕内容', () => {
        const subtitles: Subtitle[] = [
          {
            uuid: '1',
            id: 1,
            startTime: '00:00:01,000',
            endTime: '00:00:02,000',
            text: 'Hello',
            translationText: '你好',
          },
          {
            uuid: '2',
            id: 2,
            startTime: '00:00:02,000',
            endTime: '00:00:03,000',
            text: '',
            translationText: '',
          },
        ]

        const result = validateSubtitleContentDetailed(subtitles)
        
        expect(result.isValid).toBe(false)
        expect(result.contentErrors).toHaveLength(1)
        expect(result.contentErrors[0].index).toBe(1)
        expect(result.errorMessages).toContain('第2行')
      })
    })

    describe('validateComplete', () => {
      test('应该完整验证字幕和媒体文件', () => {
        const validSubtitles: Subtitle[] = [
          {
            uuid: '1',
            id: 1,
            startTime: '00:00:01,000',
            endTime: '00:00:02,000',
            text: 'Hello',
            translationText: '你好',
          },
        ]
        
        const mediaFile = new File([''], 'test.mp3', { type: 'audio/mp3' })
        
        const result = validateComplete(validSubtitles, mediaFile)
        
        expect(result.isValid).toBe(true)
        expect(result.hasEmptyContent).toBe(false)
        expect(result.hasTimeErrors).toBe(false)
        expect(result.hasNoMediaFile).toBe(false)
      })

      test('应该检测缺少媒体文件', () => {
        const validSubtitles: Subtitle[] = [
          {
            uuid: '1',
            id: 1,
            startTime: '00:00:01,000',
            endTime: '00:00:02,000',
            text: 'Hello',
            translationText: '你好',
          },
        ]
        
        const result = validateComplete(validSubtitles, null)
        
        expect(result.isValid).toBe(false)
        expect(result.hasNoMediaFile).toBe(true)
        expect(result.errorMessage).toBe('需要上传媒体文件，才能完成处理')
      })

      test('应该检测空内容', () => {
        const emptySubtitles: Subtitle[] = [
          {
            uuid: '1',
            id: 1,
            startTime: '00:00:01,000',
            endTime: '00:00:02,000',
            text: '',
            translationText: '',
          },
        ]
        
        const mediaFile = new File([''], 'test.mp3', { type: 'audio/mp3' })
        
        const result = validateComplete(emptySubtitles, mediaFile)
        
        expect(result.isValid).toBe(false)
        expect(result.hasEmptyContent).toBe(true)
        expect(result.errorMessage).toContain('缺少填写内容')
      })
    })

    describe('validateSubtitleTimeRange', () => {
      test('应该验证字幕时间范围', () => {
        const validSubtitle: Subtitle = {
          uuid: '1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Hello',
          translationText: '你好',
        }
        
        const invalidSubtitle: Subtitle = {
          uuid: '1',
          id: 1,
          startTime: '00:00:02,000',
          endTime: '00:00:01,000',
          text: 'Hello',
          translationText: '你好',
        }

        expect(validateSubtitleTimeRange(validSubtitle).valid).toBe(true)
        expect(validateSubtitleTimeRange(invalidSubtitle).valid).toBe(false)
      })
    })
  })

  // ============================================================================
  // 质量检查函数测试
  // ============================================================================

  describe('质量检查函数', () => {
    describe('detectTranslationErrors', () => {
      test('应该检测翻译错误', () => {
        const errors1 = detectTranslationErrors('Hello world', '')
        expect(errors1).toHaveLength(1)
        expect(errors1[0].type).toBe('missing')
        expect(errors1[0].severity).toBe('high')

        const errors2 = detectTranslationErrors('Hello', 'This is a very long translation that is much longer than the original text and might indicate a problem')
        expect(errors2).toHaveLength(1)
        expect(errors2[0].type).toBe('inconsistent')
        expect(errors2[0].severity).toBe('medium')
      })
    })

    describe('hasFormatIssues', () => {
      test('应该检测格式问题', () => {
        expect(hasFormatIssues('normal text')).toBe(false)
        expect(hasFormatIssues('text with @@@ symbols')).toBe(true)
        expect(hasFormatIssues('text with ### symbols')).toBe(true)
      })
    })

    describe('checkTranslationQuality', () => {
      test('应该检查翻译质量', () => {
        const quality = checkTranslationQuality('Hello world', '你好世界')
        expect(quality.score).toBeGreaterThan(0)
        expect(quality.confidence).toBeGreaterThan(0)
        expect(Array.isArray(quality.suggestions)).toBe(true)
      })
    })

    describe('calculateQualityScore', () => {
      test('应该计算质量分数', () => {
        expect(calculateQualityScore('', '')).toBe(0)
        expect(calculateQualityScore('Hello', '你好')).toBe(0.8)
        expect(calculateQualityScore('Hello', 'This is way too long for a simple hello')).toBe(0.3)
      })
    })

    describe('calculateConfidence', () => {
      test('应该计算置信度', () => {
        expect(calculateConfidence('', '')).toBe(0)
        expect(calculateConfidence('Hello', '你好')).toBe(0.75)
      })
    })

    describe('generateSuggestions', () => {
      test('应该生成建议', () => {
        const suggestions1 = generateSuggestions('Hello', '')
        expect(suggestions1).toContain('缺少翻译内容')

        const suggestions2 = generateSuggestions('Hello world', '你好')
        expect(suggestions2).toContain('翻译可能过于简短')
      })
    })

    describe('checkSubtitleQuality', () => {
      test('应该检查字幕质量', () => {
        const quality1 = checkSubtitleQuality('Hello world', '你好世界')
        expect(quality1.score).toBeGreaterThan(0)
        expect(quality1.issues).toHaveLength(0)

        const quality2 = checkSubtitleQuality('', '')
        expect(quality2.score).toBe(0)
        expect(quality2.issues).toHaveLength(1)
        expect(quality2.issues[0].type).toBe('empty')
      })

      test('应该检测过短内容', () => {
        const quality = checkSubtitleQuality('a')
        expect(quality.issues.some(issue => issue.type === 'too_short')).toBe(true)
      })

      test('应该检测过长内容', () => {
        const longText = 'a'.repeat(250)
        const quality = checkSubtitleQuality(longText)
        expect(quality.issues.some(issue => issue.type === 'too_long')).toBe(true)
      })

      test('应该检测缺少翻译', () => {
        const quality = checkSubtitleQuality('Hello world')
        expect(quality.issues.some(issue => issue.type === 'missing_translation')).toBe(true)
      })
    })

    describe('batchCheckSubtitleQuality', () => {
      test('应该批量检查字幕质量', () => {
        const subtitles = [
          { text: 'Hello', translationText: '你好' },
          { text: '', translationText: '' },
          { text: 'World', translationText: '世界' },
        ]

        const result = batchCheckSubtitleQuality(subtitles)
        
        expect(result.results).toHaveLength(3)
        expect(result.totalIssues).toBeGreaterThan(0)
        expect(result.highSeverityIssues).toBeGreaterThan(0)
        expect(result.overallScore).toBeGreaterThan(0)
      })
    })
  })
})