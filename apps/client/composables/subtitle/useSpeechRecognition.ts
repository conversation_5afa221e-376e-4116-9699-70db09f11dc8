import { mergeRecognitionText } from './useTextUtils'

// 类型定义
export interface ApiConfig {
  appid: string
  token: string
}

export interface RecognitionUtterance {
  start_time: number
  end_time: number
  text: string
  attribute?: {
    speaker?: string
  }
}

export interface RecognitionResult {
  code: number
  message?: string
  duration: number
  utterances: RecognitionUtterance[]
}

export function useSpeechRecognition() {
  // 响应式状态
  const isProcessing = ref(false)
  const recognitionResult = ref<RecognitionResult>()
  const jobId = ref('')
  const errorMessage = ref('')

  // 提交识别任务 - 使用FormData发送multipart/form-data
  const submitRecognitionTask = async (
    file: File,
    apiConfig: ApiConfig,
    params: {
      language: string
      words_per_line: number
      max_lines: number
      caption_type: string
      use_itn: boolean
      use_punc: boolean
      use_ddc: boolean
      with_speaker_info: boolean
    },
  ): Promise<string> => {
    const searchParams = new URLSearchParams({
      appid: apiConfig.appid,
      language: params.language,
      words_per_line: params.words_per_line.toString(),
      max_lines: params.max_lines.toString(),
      caption_type: params.caption_type,
      use_itn: params.use_itn.toString(),
      use_punc: params.use_punc.toString(),
      use_ddc: params.use_ddc.toString(),
      with_speaker_info: params.with_speaker_info.toString(),
    })

    console.log('📁 File info:', {
      name: file.name,
      size: file.size,
      type: file.type,
    })

    // 构造FormData
    const formData = new FormData()
    formData.append('data', file)

    // 使用API路由代理
    const response = await fetch(`/api/speech/submit?${searchParams}`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer; ${apiConfig.token}`,
      },
      body: formData,
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(error || '提交任务失败')
    }

    const result = await response.json()
    console.log('%c AT 🥝 result 🥝-88', 'font-size:13px; background:#3cc682; color:#80ffc6;', JSON.stringify(result))
    if (result.message !== 'Success') {
      throw new Error(String(result.message) ?? '提交任务失败')
    }

    return result.id
  }

  // 查询识别结果 - 使用API路由
  const queryRecognitionResult = async (taskId: string, apiConfig: ApiConfig): Promise<RecognitionResult> => {
    const params = new URLSearchParams({
      appid: apiConfig.appid,
      id: taskId,
    })

    console.log('🔍 Query params:', {
      appid: apiConfig.appid,
      id: taskId,
      token: apiConfig.token,
    })

    // 使用API路由代理
    const response = await fetch(`/api/speech/query?${params}`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer; ${apiConfig.token}`,
      },
    })

    console.log('📡 Query response:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok,
    })

    if (!response.ok) {
      const error = await response.text()
      console.error('❌ Query error details:', error)
      throw new Error(error || '查询结果失败')
    }

    const result = await response.json()
    console.log('✅ Query result:', JSON.stringify(result))
    return result as RecognitionResult
  }

  // 开始识别
  const startRecognition = async (
    file: File,
    apiConfig: ApiConfig,
    recognitionParams: {
      language: string
      words_per_line: number
      max_lines: number
      caption_type: string
      use_itn: boolean
      use_punc: boolean
      use_ddc: boolean
      with_speaker_info: boolean
    },
  ) => {
    if (!file || !apiConfig.appid || !apiConfig.token || isProcessing.value)
      return

    isProcessing.value = true
    errorMessage.value = ''

    try {
      // 直接提交音频文件的二进制数据
      const taskId = await submitRecognitionTask(file, apiConfig, recognitionParams)
      jobId.value = taskId

      // 轮询查询结果
      let attempts = 0
      const maxAttempts = 60 // 最多查询60次（约5分钟）

      const pollResult = async () => {
        attempts++

        try {
          const result = await queryRecognitionResult(taskId, apiConfig)

          if (result.code === 0) {
            // 识别成功
            recognitionResult.value = result
            isProcessing.value = false
          } else if (result.code === 1001) {
            // 处理中，继续轮询
            if (attempts < maxAttempts) {
              setTimeout(() => void pollResult(), 5000) // 5秒后重试
            } else {
              throw new Error('识别超时，请稍后重试')
            }
          } else {
            // 识别失败
            throw new Error(result.message || '识别失败')
          }
        } catch (error) {
          if (attempts < maxAttempts) {
            setTimeout(() => void pollResult(), 5000) // 5秒后重试
          } else {
            throw error
          }
        }
      }

      // 延迟开始轮询
      setTimeout(() => void pollResult(), 3000)
    } catch (error) {
      console.error('识别失败:', error)
      errorMessage.value = error instanceof Error ? error.message : '识别过程中发生错误'
      isProcessing.value = false
      jobId.value = ''
    }
  }

  // 重置所有状态
  const resetRecognition = () => {
    recognitionResult.value = {
      code: 0,
      duration: 0,
      utterances: [],
    }
    jobId.value = ''
    errorMessage.value = ''
    isProcessing.value = false
  }

  // 计算属性
  const canStartRecognition = computed(() => {
    return !isProcessing.value
  })

  // 合并识别结果
  const mergeRecognitionResult = () => {
    if (!recognitionResult.value)
      return ''
    const content = mergeRecognitionText(recognitionResult.value)
    return content
  }

  return {
    // 响应式状态
    isProcessing,
    recognitionResult,
    jobId,
    errorMessage,

    // 计算属性
    canStartRecognition,

    // 方法
    startRecognition,
    resetRecognition,
    mergeRecognitionResult,
  }
}
