import { vi } from 'vitest'

// Mock global constants to prevent import issues
vi.mock('~/utils/shared/constants', () => ({
  SUMMARY_LENGTH: 8000,
  DEFAULT_SUBTITLE_START_TIME: '00:00:00,000',
  DEFAULT_SUBTITLE_END_TIME: '00:00:02,000',
  DEFAULT_SUBTITLE_DURATION: 2,
  SUBTITLE_MERGE_SEPARATOR: ' ',
  SUPPORTED_AUDIO_FORMATS: ['wav', 'mp3', 'mp4', 'm4a', 'ogg'],
  SUPPORTED_MIME_TYPES: ['audio/wav', 'audio/mpeg', 'audio/mp4'],
  EXTENSION_TO_MIME_MAP: { wav: 'audio/wav', mp3: 'audio/mpeg' },
  MIME_TYPE_NORMALIZATION_MAP: { 'audio/mp3': 'audio/mpeg' },
  DEFAULT_TEMPERATURE: 0.3,
  DEFAULT_TOP_P: 0.9,
  SRC_LANG: 'en',
  TGT_LANG: '简体中文',
  DEEP_SEEK_MODEL_ID: 'deepseek-v3-250324',
  DEFAULT_CHUNK_SIZE: 600,
  DEFAULT_MAX_SENTENCES: 10,
  CONTEXT_PREVIOUS_LINES: 3,
  CONTEXT_AFTER_LINES: 2,
  SIMILARITY_THRESHOLD: 0.9,
  SIMILARITY_WARNING_THRESHOLD: 0.95,
  MAX_RETRY_ATTEMPTS: 3,
  RETRY_DELAY_BASE: 1000,
  TRANSLATION_LENGTH_RATIO_MIN: 0.3,
  TRANSLATION_LENGTH_RATIO_MAX: 3.0,
  QUALITY_SCORE_THRESHOLD: 70,
  AUDIO_MIME_PREFIX: 'audio/',
  VIDEO_MIME_PREFIX: 'video/',
  END_TIME_ADJUSTMENT: 0.01,
}))

// Mock LRC store
vi.mock('~/composables/subtitle/stores/lrcStore', () => ({
  useLrcStore: vi.fn(() => ({
    lrcLines: { value: [] },
    hasLrc: { value: false },
    uploadLrc: vi.fn(),
    updateLrcContent: vi.fn(),
    parseLrcContent: vi.fn(),
  })),
}))

// Mock subtitle store
vi.mock('~/composables/subtitle/stores/subtitleStore', () => ({
  useSubtitleStore: vi.fn(() => ({
    subtitles: { value: [] },
    historySubtitles: { value: [] },
    future: { value: [] },
    canUndo: { value: false },
    canRedo: { value: false },
    filteredSubtitles: { value: [] },
    addSubtitle: vi.fn(),
    deleteSubtitle: vi.fn(),
    mergeWithNextSubtitle: vi.fn(),
    updateSubtitleText: vi.fn(),
    updateSubtitleTranslationText: vi.fn(),
    updateSubtitleStartTime: vi.fn(),
    updateSubtitleEndTime: vi.fn(),
    setSubtitles: vi.fn(),
  })),
}))

// Mock player store
vi.mock('~/composables/subtitle/stores/playerStore', () => ({
  usePlayerStore: vi.fn(() => ({
    status: { value: 'idle' },
    currentTime: { value: 0 },
    duration: { value: 0 },
    mediaUrl: { value: null },
    mediaFile: { value: null },
    mediaFileHash: { value: null },
    originalMediaFileHash: { value: null },
    error: { value: null },
    playbackRate: { value: 1 },
    seekRequest: { value: null },
    isLooping: { value: false },
    loopingSubtitleUuid: { value: null },
    loopStartTime: { value: null },
    loopEndTime: { value: null },
    practicePlayingUuid: { value: null },
    practiceEndTime: { value: null },
    isPlaying: { value: false },
    isPracticePlaying: { value: false },
    setMediaFile: vi.fn(),
    setMediaUrl: vi.fn(),
    setMediaUrlWithProxy: vi.fn(),
    setOriginalMediaFileHash: vi.fn(),
    setDuration: vi.fn(),
    setStatus: vi.fn(),
    setError: vi.fn(),
    seek: vi.fn(),
    updatePlayIngStatus: vi.fn(),
    updatePauseStatus: vi.fn(),
    togglePlay: vi.fn(),
    clearMedia: vi.fn(),
    setPlaybackRate: vi.fn(),
    setSeekRequest: vi.fn(),
    startLoop: vi.fn(),
    stopLoop: vi.fn(),
    updateLoopTime: vi.fn(),
    startPracticePlay: vi.fn(),
    stopPracticePlay: vi.fn(),
  })),
}))

// Mock other common stores
vi.mock('~/stores/course', () => ({
  useCourseStore: vi.fn(() => ({
    currentCourse: {
      id: 'course-1',
      coursePackId: 'pack-1',
      title: 'Test Course',
      description: 'Test Description',
      sentences: [],
      mediaUrl: 'test-url',
    },
    updateCourseData: vi.fn(),
    init: vi.fn(),
  })),
}))

vi.mock('~/stores/coursePack', () => ({
  useCoursePackStore: vi.fn(() => ({
    init: vi.fn(),
  })),
}))

vi.mock('~/stores/gameUpdate', () => ({
  useGameUpdateStore: vi.fn(() => ({
    markCourseForUpdate: vi.fn(),
  })),
}))

// Mock Nuxt functions globally
global.useNuxtApp = vi.fn(() => ({
  $trpc: {
    sentence: {
      batchUpsertSentences: { mutate: vi.fn(async () => Promise.resolve({ sentences: [] })) },
      deleteBatchSentences: { mutate: vi.fn(async () => Promise.resolve({ failedIds: [] })) },
      movePosition: { mutate: vi.fn(async () => Promise.resolve()) },
      batchUpdateElementsTime: { mutate: vi.fn(async () => Promise.resolve()) },
    },
    course: {
      findOne: { query: vi.fn(async () => Promise.resolve({ id: 'course-1', sentences: [] })) },
      edit: { mutate: vi.fn(async () => Promise.resolve()) },
    },
    speech: {
      submit: { mutate: vi.fn(async () => Promise.resolve({ id: 'task-1', message: 'Success' })) },
      query: { query: vi.fn(async () => Promise.resolve({ code: 0, message: 'Success', utterances: [] })) },
    },
  },
}))

global.useRuntimeConfig = vi.fn(() => ({
  public: {
    s3: { bucketGameCDN: 'https://cdn.example.com/' },
  },
}))

global.useRoute = vi.fn(() => ({
  query: {
    coursePackId: 'pack-1',
    courseId: 'course-1',
  },
}))

// Mock DOM
global.document = {
  createElement: vi.fn(tag => ({
    style: { cssText: '' },
    textContent: '',
    innerHTML: '',
    appendChild: vi.fn(),
    querySelector: vi.fn(),
  })),
  getElementById: vi.fn(),
} as any

global.URL = {
  createObjectURL: vi.fn(() => 'blob:test-url'),
  revokeObjectURL: vi.fn(),
} as any

global.window = {
  setInterval: vi.fn(),
  clearInterval: vi.fn(),
} as any
